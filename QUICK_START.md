# 🚀 Quick Start Guide - Auto-Opening Swagger

This guide shows you how to start your Common Class API with automatic Swagger documentation opening.

## 🎯 Best Experience (Recommended)

### Option 1: Enhanced Startup Script
```bash
python start_with_swagger.py
```

**What it does:**
- ✅ Starts the FastAPI server
- ✅ Waits for server to be ready
- ✅ Automatically opens Swagger UI in your browser
- ✅ Shows helpful status messages and quick links
- ✅ Handles errors gracefully

### Option 2: Platform-Specific Scripts

#### Windows (Batch)
```cmd
run_all.bat
```

#### Windows (PowerShell)
```powershell
.\run_all.ps1
```

#### Cross-Platform (Make)
```bash
make dev
```

#### Simple Python Runner
```bash
python run.py
```

## 🔧 What Happens When You Start

1. **Server Startup**: FastAPI server starts on `http://127.0.0.1:8000`
2. **Health Check**: <PERSON><PERSON><PERSON> waits for `/health` endpoint to respond
3. **Browser Opening**: Automatically opens `http://127.0.0.1:8000/docs`
4. **Ready to Use**: Swagger UI loads with your API documentation

## 📖 Quick Links (Auto-Available)

Once started, these URLs will be available:

- **📖 Swagger UI**: `http://127.0.0.1:8000/docs`
- **📋 API Endpoints**: `http://127.0.0.1:8000/available-endpoints`
- **🔍 Service Discovery**: `http://127.0.0.1:8000/services`
- **❤️ Health Check**: `http://127.0.0.1:8000/health`
- **🏠 API Home**: `http://127.0.0.1:8000/`

## 🛠️ Troubleshooting

### Browser Doesn't Open Automatically
- **Cause**: Browser security settings or no default browser
- **Solution**: Manually open `http://127.0.0.1:8000/docs`

### Port Already in Use
- **Cause**: Another service is using port 8000
- **Solution**: The script will detect this and open the existing server

### Server Takes Too Long to Start
- **Cause**: System resources or dependency issues
- **Solution**: Check the console for error messages

## 🎨 Customization

### Change Port
Edit the startup scripts and change:
```python
port = 8000  # Change to your preferred port
```

### Change Host
Edit the startup scripts and change:
```python
host = "127.0.0.1"  # Change to "0.0.0.0" for external access
```

### Disable Auto-Open
Use the manual startup method:
```bash
uvicorn app.main:app --reload --host 127.0.0.1 --port 8000
```

## 🚀 Production Deployment

For production deployment with your domain `common_class_api.ultraagent.app`, see:
- `DEPLOYMENT.md` - Complete deployment guide
- `docker-compose.yml` - Docker deployment
- `render.yaml` - Render.com deployment

## 💡 Tips

1. **First Time Setup**: Use `start_with_swagger.py` for the best experience
2. **Development**: Use `run.py` or `make dev` for quick restarts
3. **Testing**: The auto-open feature helps you immediately test your API
4. **Documentation**: Swagger UI provides interactive API testing

## 🎉 That's It!

Your Common Class API is now ready with auto-opening Swagger documentation. Happy coding! 🚀
