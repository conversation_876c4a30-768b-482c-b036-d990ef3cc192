#!/usr/bin/env python3
"""
Enhanced startup script for Common Class API with automatic Swagger opening.
This script provides the best user experience by:
1. Starting the FastAPI server
2. Waiting for it to be ready
3. Automatically opening Swagger documentation in the browser
4. Providing helpful status messages
"""

import uvicorn
import webbrowser
import threading
import time
import httpx
import sys
import os
from typing import Optional

def print_banner():
    """Print a nice startup banner."""
    print("=" * 60)
    print("🚀 Common Class API - Enhanced Startup")
    print("=" * 60)
    print()

def check_server_health(url: str, timeout: int = 1) -> bool:
    """Check if the server is responding to health checks."""
    try:
        with httpx.Client() as client:
            response = client.get(f"{url}/health", timeout=timeout)
            return response.status_code == 200
    except (httpx.RequestError, httpx.HTTPStatusError):
        return False

def wait_for_server_and_open_browser(url: str, max_wait: int = 30) -> None:
    """Wait for the server to be ready and then open the browser."""
    def check_and_open():
        print("⏳ Waiting for server to start...")
        
        for i in range(max_wait):
            if check_server_health(url):
                print(f"✅ Server is ready! (took {i+1} seconds)")
                print(f"🌐 Opening Swagger documentation: {url}/docs")
                
                # Open the browser
                try:
                    webbrowser.open(f"{url}/docs")
                    print("📖 Swagger UI opened in your default browser!")
                except Exception as e:
                    print(f"⚠️  Could not auto-open browser: {e}")
                    print(f"📖 Please manually open: {url}/docs")
                
                print()
                print("🔗 Quick Links:")
                print(f"   📖 Swagger UI: {url}/docs")
                print(f"   📋 API Endpoints: {url}/available-endpoints")
                print(f"   🔍 Service Discovery: {url}/services")
                print(f"   ❤️  Health Check: {url}/health")
                print()
                print("🛑 Press Ctrl+C to stop the server")
                print("=" * 60)
                break
            
            # Show progress dots
            if i % 3 == 0:
                print(".", end="", flush=True)
            
            time.sleep(1)
        else:
            print(f"⚠️  Server didn't start within {max_wait} seconds")
            print(f"📖 Try manually opening: {url}/docs")
    
    # Start the browser opening in a separate thread
    threading.Thread(target=check_and_open, daemon=True).start()

def main():
    """Main startup function."""
    # Configuration
    host = "127.0.0.1"
    port = 8000
    server_url = f"http://{host}:{port}"
    
    # Print banner
    print_banner()
    
    # Check if port is already in use
    if check_server_health(server_url):
        print(f"⚠️  Port {port} is already in use!")
        print(f"🌐 Opening existing server: {server_url}/docs")
        webbrowser.open(f"{server_url}/docs")
        return
    
    print(f"🚀 Starting Common Class API on {server_url}")
    print(f"📖 Swagger documentation will auto-open at: {server_url}/docs")
    print()
    
    # Start the browser opening process
    wait_for_server_and_open_browser(server_url)
    
    try:
        # Start the server
        uvicorn.run(
            "app.main:app",
            host=host,
            port=port,
            reload=True,
            log_level="info",
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        print("👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
