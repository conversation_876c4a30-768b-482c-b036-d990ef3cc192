#!/usr/bin/env python3
"""
Simple runner script for the Common Class API.
This allows you to run the application directly with: python run.py
"""

import uvicorn
import webbrowser
import threading
import time
import httpx

def wait_for_server_and_open_browser(url, max_wait=30):
    """Wait for the server to be ready and then open the browser."""
    def check_and_open():
        for _ in range(max_wait):
            try:
                with httpx.Client() as client:
                    response = client.get(f"{url}/health", timeout=1)
                    if response.status_code == 200:
                        print(f"🌐 Opening Swagger documentation: {url}/docs")
                        webbrowser.open(f"{url}/docs")
                        break
            except (httpx.RequestError, httpx.HTTPStatusError):
                pass
            time.sleep(1)

    # Start the browser opening in a separate thread
    threading.Thread(target=check_and_open, daemon=True).start()

if __name__ == "__main__":
    host = "127.0.0.1"
    port = 8000
    server_url = f"http://{host}:{port}"

    print("🚀 Starting Common Class API...")
    print(f"📖 Swagger documentation will auto-open at: {server_url}/docs")
    print("🛑 Press Ctrl+C to stop the server")
    print()

    # Start the browser opening process
    wait_for_server_and_open_browser(server_url)

    # Start the server
    uvicorn.run(
        "app.main:app",
        host=host,
        port=port,
        reload=True,
        log_level="info"
    )
