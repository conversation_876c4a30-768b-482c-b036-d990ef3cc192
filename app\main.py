from importlib import import_module
import inspect
import os
from typing import Dict, List, Any, Optional
from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException, Body, Query
from fastapi.responses import JSONResponse
from fastapi.routing import APIRoute
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# Handle both relative and absolute imports
try:
    from .core.security import verify_token
except ImportError:
    from app.core.security import verify_token

# Flexible request model for dynamic endpoints
class DynamicRequest(BaseModel):
    """
    Flexible request model that accepts any JSON payload.

    Common parameters for LINE service:
    - messages: str or list - Message content
    - token: str - LINE Bot Channel Access Token (optional)
    - to: str - Target user/group/room ID (for push/reply)
    - reply_token: str - Reply token (for reply messages)

    For other services, refer to the /services endpoint for parameter details.
    """
    class Config:
        extra = "allow"  # Allow any additional fields

app = FastAPI(
    title="Common Class API",
    description="""
    # 🚀 Common Class API - Universal Service Integration Platform

    A powerful plug-and-play FastAPI microservice that exposes reusable helper classes through dynamic routes.
    Perfect for integrating multiple services with a single, unified API interface.

    ## 🎯 Dynamic Endpoints

    This API uses **dynamic routing** with the pattern: `/{service}.{method}`

    ### 📱 Available Services:

    | Service | Description | Key Features |
    |---------|-------------|--------------|
    | **facebook** | Facebook Graph API integration | Post text, images, videos to Facebook |
    | **line** | LINE messaging service | Broadcast to all friends, push messages, reply |
    | **autoit** | Windows automation service | GUI automation and scripting |
    | **minio** | Object storage service | File uploads and management |
    | **nocodb** | Database service | Database operations and queries |
    | **openai** | OpenAI API integration | AI-powered requests and responses |

    ## 🖼️ **NEW: AI Vision Integration**

    **🔥 Latest Feature:** `openai.generate` with **Vision Capabilities**

    Automatically analyze **images** from NocoDB and generate beautiful content using **OpenAI Vision (GPT-4V)**!

    ### 🎯 **Perfect for:**
    - **Product Images** → Product descriptions
    - **Photos** → Social media captions
    - **Screenshots** → Feature explanations
    - **Artwork** → Creative descriptions
    - **Any Image** → Engaging content

    ### 🚀 **How it works:**
    ```
    1. Store image URL in NocoDB
    2. Click button: /openai.generate?id={{row.id}}
    3. AI analyzes image with GPT-4V
    4. Beautiful content generated automatically
    5. Content saved back to NocoDB
    ```

    ### 🧪 **Ready to Test:**
    **Your NocoDB Configuration:**
    - **Base URL**: `http://www.amibigeye.com:8080`
    - **Table ID**: `movxrk3kdcbkyo8`
    - **Test Record**: `id=2`
    - **Token**: Configured ✅

    **🚀 Quick Test URLs:**
    - **Main Test**: `/openai.generate?id=2`
    - **Connection Test**: `/test/nocodb?record_id=2`
    - **Any Record**: `/openai.generate?id=YOUR_RECORD_ID`

    ### 📋 **Testing Steps:**
    1. **🧪 Test Connection**: Use `/test/nocodb?record_id=2` to verify NocoDB access
    2. **🚀 Generate Content**: Use `/openai.generate?id=2` to create AI content
    3. **✅ Check Results**: Verify content was saved to `adjust_content` field
    4. **🎯 Try More**: Test with different record IDs from your table

    ## 🌟 Featured: LINE Broadcasting

    **🔥 Popular Method:** `line.send_to_all_friends`

    Send text, image, and/or video messages to **all friends** of a LINE token in one API call!

    **Parameters:**
    - `token` (required): LINE Channel Access Token
    - `message` (optional): Text message to send
    - `img_url` (optional): Image URL to send
    - `vd_url` (optional): Video URL to send

    **Example Usage:**
    ```
    POST /line.send_to_all_friends
    {
        "token": "your_line_token",
        "message": "Hello everyone! 🎉",
        "img_url": "https://example.com/image.jpg",
        "vd_url": "https://example.com/video.mp4"
    }
    ```

    ## 🆕 Featured: Facebook Universal Post

    **New Method:** `facebook.post(token, text, images, videos)`

    Post text, images, and videos to your Facebook account with a single universal method!

    **Parameters:**
    - `token` (optional): Facebook access token
    - `text` (optional): Text message to post
    - `images` (optional): URL of image to post
    - `videos` (optional): URL of video to post

    **Example Usage:**
    ```
    POST /facebook.post
    {
        "token": "your_facebook_token",
        "text": "Hello Facebook! 🚀",
        "images": "https://example.com/image.jpg"
    }
    ```

    **Other Examples:**
    ```
    POST /facebook.post_text
    POST /facebook.post_image
    POST /facebook.post_video
    POST /openai.request
    POST /openai.vision                  (🖼️ NEW: Analyze images with AI)
    POST /line.broadcast
    POST /line.test_send_to_all_friends  (for testing without real API calls)
    GET  /openai.generate?id={{row.id}}  (🖼️ NEW: NocoDB + OpenAI Vision integration)
    ```

    ## 🔍 Discovery Endpoints

    - **`/available-endpoints`** - List all available service methods
    - **`/services`** - Detailed service information with method signatures
    - **`/routes`** - FastAPI registered routes
    - **`/docs`** - Interactive Swagger documentation (this page!)
    - **`/redoc`** - Alternative ReDoc documentation

    ## 🔐 Authentication

    All endpoints require Bearer token authentication:
    ```
    Authorization: Bearer YOUR_API_TOKEN
    ```

    **Happy coding! 🚀**
    """,
    version="1.0.3",
    contact={
        "name": "Common Class API Support",
        "url": "https://common_class_api.ultraagent.app",
        "email": "<EMAIL>"
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT"
    },
    servers=[
        {
            "url": "https://common_class_api.ultraagent.app",
            "description": "Production server"
        },
        {
            "url": "http://localhost:8000",
            "description": "Development server"
        }
    ],
    tags_metadata=[
        {
            "name": "General",
            "description": "General API information and health checks"
        },
        {
            "name": "Discovery",
            "description": "Service discovery and endpoint exploration"
        },
        {
            "name": "Facebook Documentation",
            "description": "Detailed Facebook service documentation and examples"
        },
        {
            "name": "LINE Documentation",
            "description": "Detailed LINE service documentation and examples"
        },
        {
            "name": "OpenAI Vision",
            "description": "🖼️ NEW: AI Vision capabilities for image analysis and automated content generation"
        },
        {
            "name": "NocoDB Integration",
            "description": "🖼️ Database integration with automated AI content generation from images"
        },
        {
            "name": "Testing",
            "description": "🧪 Test endpoints for verifying configuration and connectivity"
        }
    ]
)

# Add CORS middleware to handle browser requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify your domains
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/test/nocodb", dependencies=[Depends(verify_token)], tags=["Testing"], summary="🧪 Test NocoDB Connection")
async def test_nocodb_connection(
    table_id: str = Query(default="movxrk3kdcbkyo8", description="The NocoDB table ID to test"),
    record_id: str = Query(default="2", description="The record ID to test")
):
    """
    🧪 **Test NocoDB Connection**

    This endpoint tests the connection to your NocoDB instance and verifies that records can be fetched.

    **Your Configuration:**
    - **Base URL**: `http://www.amibigeye.com:8080`
    - **Token**: `P2fO__pxZ9PjOgZXFpZlLLa7PoAsDX3R_Hxr26ct`
    - **Default Table**: `movxrk3kdcbkyo8`
    - **Test Record**: `2`

    **What this test does:**
    1. Connects to your NocoDB instance
    2. Attempts to fetch the specified record
    3. Shows available fields in the record
    4. Verifies token authentication
    5. Returns detailed connection information

    **Perfect for:**
    - ✅ Verifying NocoDB configuration
    - 🔐 Testing authentication
    - 📊 Checking record structure
    - 🐛 Debugging connection issues
    - 🎯 Finding the right record IDs
    - 📋 Seeing available fields in your records

    **🚀 Quick Test:**
    - Use `record_id=2` to test with your known record
    - Check if `Image` field has image URLs
    - Check if `Topic` field has text content
    - Verify `adjust_content` field exists for saving results
    """
    try:
        # Import NocoDB service
        nocodb_mod = import_module("app.services.nocodb_service")
        nocodb_service = nocodb_mod.Nocodb()

        # Test connection and fetch record
        fetch_result = await nocodb_service.fetch_record(table_id=table_id, record_id=record_id)

        # Prepare detailed response
        response = {
            "test_status": "success" if fetch_result["status"] == "success" else "failed",
            "connection_info": {
                "base_url": nocodb_service.base_url,
                "table_id": table_id,
                "record_id": record_id,
                "has_token": bool(nocodb_service.default_token),
                "token_preview": f"{nocodb_service.default_token[:10]}..." if nocodb_service.default_token else None
            },
            "fetch_result": fetch_result,
            "timestamp": "2024-01-01T00:00:00Z"
        }

        if fetch_result["status"] == "success":
            record_data = fetch_result["record"]
            response["record_analysis"] = {
                "total_fields": len(record_data),
                "available_fields": list(record_data.keys()),
                "has_image_field": "Image" in record_data,
                "has_topic_field": "Topic" in record_data,
                "has_content_field": "adjust_content" in record_data,
                "sample_data": {k: str(v)[:50] + "..." if len(str(v)) > 50 else v for k, v in list(record_data.items())[:5]}
            }
            response["recommendations"] = {
                "ready_for_vision": "Image" in record_data and record_data.get("Image"),
                "ready_for_text": "Topic" in record_data and record_data.get("Topic"),
                "can_update_content": "adjust_content" in record_data,
                "suggested_test_url": f"/openai.generate?id={record_id}"
            }

        return response

    except Exception as e:
        return {
            "test_status": "error",
            "error": str(e),
            "connection_info": {
                "base_url": "http://www.amibigeye.com:8080",
                "table_id": table_id,
                "record_id": record_id,
                "error_type": type(e).__name__
            },
            "troubleshooting": {
                "check_network": "Ensure the NocoDB server is accessible",
                "check_token": "Verify the NocoDB token is correct",
                "check_table": "Confirm the table ID exists",
                "check_record": "Verify the record ID exists in the table"
            }
        }


@app.get("/openai.generate", dependencies=[Depends(verify_token)], tags=["NocoDB Integration"], summary="🖼️ Generate content from NocoDB images using AI Vision")
async def openai_generate_from_nocodb(
    id: str = Query(..., description="The NocoDB record ID to process")
):
    """
    🚀 **Simple NocoDB + OpenAI Vision Integration**

    **Super Simple:** Just provide the record ID - everything else is automatic!

    **What it does:**
    1. **Fetch Record**: Gets your record from NocoDB (table: movxrk3kdcbkyo8)
    2. **Find Image**: Looks for image URL in "Image" field
    3. **AI Vision**: OpenAI analyzes the image with GPT-4V
    4. **Generate Content**: Creates beautiful content describing the image
    5. **Save Result**: Updates "adjust_content" field automatically

    **🖼️ Automatic Flow:**
    ```
    GET /openai.generate?id=2
    ↓
    Fetch record from your NocoDB
    ↓
    Extract Image URL → OpenAI Vision
    ↓
    AI sees image and writes content
    ↓
    Save to adjust_content field
    ↓
    Done! ✅
    ```

    **📋 Your NocoDB Setup:**
    - **Table ID**: movxrk3kdcbkyo8 (configured)
    - **Image Field**: "Image" (where image URLs are stored)
    - **Content Field**: "adjust_content" (where AI content is saved)
    - **Fallback Field**: "Topic" (used if no image found)

    **🎯 Perfect for:**
    - Product images → Product descriptions
    - Photos → Social media captions
    - Screenshots → Feature explanations
    - Any image → Engaging content

    **Usage in NocoDB Button:**
    ```
    https://common_class_api.ultraagent.app/openai.generate?id={{row.id}}
    ```

    **That's it! No complex parameters needed.** 🎨✨
    """
    try:
        # Configuration - all defaults are set here
        table_id = "movxrk3kdcbkyo8"
        image_field = "Image"
        topic_field = "Topic"
        content_field = "adjust_content"
        use_vision = True
        vision_prompt = "Analyze this image and write beautiful, engaging content describing what you see. Include details about the visual elements, mood, and any interesting aspects."
        prompt_template = "Write beautiful content about: {topic}"

        # Import services
        nocodb_mod = import_module("app.services.nocodb_service")
        openai_mod = import_module("app.services.openai_service")

        nocodb_service = nocodb_mod.Nocodb()
        openai_service = openai_mod.Openai()

        # Step 1: Fetch record from NocoDB
        fetch_result = await nocodb_service.fetch_record(table_id=table_id, record_id=id)

        if fetch_result["status"] != "success":
            return {
                "status": "error",
                "step": "fetch_record",
                "message": f"Failed to fetch record: {fetch_result.get('message', 'Unknown error')}",
                "details": fetch_result
            }

        record_data = fetch_result["record"]

        # Step 2: Determine content generation method (Vision vs Text)
        image_url = record_data.get(image_field) if use_vision else None
        topic = record_data.get(topic_field)

        # Check if we have the required data
        if use_vision and not image_url:
            # Try fallback to text-based generation
            if not topic:
                return {
                    "status": "error",
                    "step": "extract_data",
                    "message": f"Neither image field '{image_field}' nor topic field '{topic_field}' found or empty in record",
                    "available_fields": list(record_data.keys()),
                    "record_id": id,
                    "note": "For image-based generation, provide an image URL. For text-based generation, provide a topic."
                }
            # Fall back to text-based generation
            use_vision_actual = False
            generation_method = "text (fallback)"
        elif use_vision and image_url:
            use_vision_actual = True
            generation_method = "vision"
        elif not use_vision and not topic:
            return {
                "status": "error",
                "step": "extract_topic",
                "message": f"Topic field '{topic_field}' not found or empty in record",
                "available_fields": list(record_data.keys()),
                "record_id": id
            }
        else:
            use_vision_actual = False
            generation_method = "text"

        # Step 3: Generate content using appropriate method
        if use_vision_actual:
            # Use OpenAI Vision to analyze the image
            openai_result = await openai_service.vision(
                message=vision_prompt,
                image_url=image_url
            )
        else:
            # Use traditional text-based generation
            prompt = prompt_template.format(topic=topic)
            openai_result = await openai_service.chat(message=prompt)

        if openai_result["status"] != "success":
            return {
                "status": "error",
                "step": "openai_generation",
                "message": f"Failed to generate content: {openai_result.get('message', 'Unknown error')}",
                "generation_method": generation_method,
                "topic": topic,
                "image_url": image_url if use_vision_actual else None,
                "details": openai_result
            }

        generated_content = openai_result["content"]

        # Step 4: Update the record with generated content
        update_data = {content_field: generated_content}
        update_result = await nocodb_service.update_record(
            table_id=table_id,
            record_id=id,
            data=update_data
        )

        if update_result["status"] != "success":
            return {
                "status": "error",
                "step": "update_record",
                "message": f"Failed to update record: {update_result.get('message', 'Unknown error')}",
                "generated_content": generated_content,
                "details": update_result
            }

        # Success! Return comprehensive result
        return {
            "status": "success",
            "message": f"Successfully generated and updated content for record {id} using {generation_method}",
            "flow_summary": {
                "record_id": id,
                "table_id": table_id,
                "generation_method": generation_method,
                "image_analyzed": image_url if use_vision_actual else None,
                "topic_used": topic if not use_vision_actual else None,
                "content_generated": len(generated_content),
                "field_updated": content_field,
                "vision_enabled": use_vision_actual
            },
            "generation_details": {
                "method": generation_method,
                "image_url": image_url if use_vision_actual else None,
                "topic": topic if not use_vision_actual else None,
                "prompt_used": vision_prompt if use_vision_actual else prompt_template.format(topic=topic) if topic else None,
                "model_used": openai_result.get("model_used", "unknown")
            },
            "generated_content": generated_content,
            "updated_record": update_result["record"],
            "timestamp": "2024-01-01T00:00:00Z"
        }

    except Exception as e:
        return {
            "status": "error",
            "step": "general_error",
            "message": f"Unexpected error in content generation flow: {str(e)}",
            "record_id": id,
            "error": str(e)
        }


@app.post("/{object_function:path}", dependencies=[Depends(verify_token)])
async def dynamic_endpoint(
    object_function: str,
    payload: DynamicRequest = Body(
        default_factory=dict,
        description="Request payload - parameters depend on the service and method being called",
        examples={
            "line.broadcast": {
                "summary": "LINE Broadcast Message",
                "description": "Send a message to all friends",
                "value": {
                    "messages": "Hello everyone! 🎉"
                }
            },
            "line.push": {
                "summary": "LINE Push Message",
                "description": "Send a message to specific user",
                "value": {
                    "to": "user_id_here",
                    "messages": "Hello there!"
                }
            },
            "line.send_to_all_friends": {
                "summary": "LINE Send All Content Types to Friends",
                "description": "Send text, image, and/or video messages to all friends using a specific LINE token",
                "value": {
                    "token": "your_line_channel_access_token",
                    "message": "🎉 Hello everyone! Check out this content!",
                    "img_url": "https://example.com/image.jpg",
                    "vd_url": "https://example.com/video.mp4"
                }
            },
            "line.send_to_all_friends_text_only": {
                "summary": "LINE Send Text Only to All Friends",
                "description": "Send only a text message to all friends",
                "value": {
                    "token": "your_line_channel_access_token",
                    "message": "Hello everyone! This is a text-only message."
                }
            },
            "line.send_to_all_friends_with_image": {
                "summary": "LINE Send Text + Image to All Friends",
                "description": "Send text message with an image to all friends",
                "value": {
                    "token": "your_line_channel_access_token",
                    "message": "📸 Check out this amazing image!",
                    "img_url": "https://picsum.photos/800/600"
                }
            },
            "line.test_send_to_all_friends": {
                "summary": "LINE Test Send to All Friends (No Real API Call)",
                "description": "Test the send_to_all_friends method without making actual LINE API calls",
                "value": {
                    "token": "test_token_123",
                    "message": "This is a test message",
                    "img_url": "https://example.com/test-image.jpg",
                    "vd_url": "https://example.com/test-video.mp4"
                }
            },
            "line.test_send_to_all_friends_text_only": {
                "summary": "LINE Test Text Only (No Real API Call)",
                "description": "Test sending only text message without making actual LINE API calls",
                "value": {
                    "token": "test_token_123",
                    "message": "Just testing text message functionality"
                }
            },
            "line.test_send_to_all_friends_image_only": {
                "summary": "LINE Test Image Only (No Real API Call)",
                "description": "Test sending only image without making actual LINE API calls",
                "value": {
                    "token": "test_token_123",
                    "img_url": "https://picsum.photos/800/600"
                }
            },
            "line.test_send_to_all_friends_video_only": {
                "summary": "LINE Test Video Only (No Real API Call)",
                "description": "Test sending only video without making actual LINE API calls",
                "value": {
                    "token": "test_token_123",
                    "vd_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
                }
            },
            "line.test_send_to_all_friends_error_case": {
                "summary": "LINE Test Error Case (No Real API Call)",
                "description": "Test error handling when no content is provided",
                "value": {
                    "token": "test_token_123"
                }
            },
            "line.test_method": {
                "summary": "LINE Test Method",
                "description": "Test the LINE service",
                "value": {
                    "test_param": "hello world"
                }
            },
            "facebook.post": {
                "summary": "Facebook Universal Post",
                "description": "Post text, images, or videos to Facebook using the universal post method",
                "value": {
                    "token": "your_facebook_access_token",
                    "text": "Hello Facebook! 👋",
                    "images": "https://picsum.photos/800/600"
                }
            },
            "facebook.post_text": {
                "summary": "Facebook Text Post",
                "description": "Post text-only message to Facebook",
                "value": {
                    "text": "Hello Facebook from API! 🚀"
                }
            },
            "facebook.post_image": {
                "summary": "Facebook Image Post",
                "description": "Post image with caption to Facebook",
                "value": {
                    "images": "https://picsum.photos/800/600",
                    "text": "Check out this beautiful image! 📸"
                }
            },
            "facebook.post_video": {
                "summary": "Facebook Video Post",
                "description": "Post video with title and description to Facebook",
                "value": {
                    "videos": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
                    "text": "Amazing video content! 🎬"
                }
            },
            "facebook.get_service_info": {
                "summary": "Facebook Service Info",
                "description": "Get information about the Facebook service and available methods",
                "value": {}
            },
            "openai.request": {
                "summary": "OpenAI Request",
                "description": "Make an OpenAI API request",
                "value": {
                    "message": "Hello, AI!",
                    "token": "your-openai-token"
                }
            },
            "openai.vision": {
                "summary": "🖼️ OpenAI Vision Analysis",
                "description": "Analyze images using OpenAI Vision (GPT-4V) - NEW FEATURE!",
                "value": {
                    "message": "What do you see in this image? Describe it in detail.",
                    "image_url": "https://picsum.photos/800/600",
                    "token": "your-openai-token"
                }
            },
            "openai.chat": {
                "summary": "OpenAI Chat Completion",
                "description": "Generate text using OpenAI chat models",
                "value": {
                    "message": "Write a creative story about technology",
                    "model": "gpt-4o-mini",
                    "max_tokens": 500
                }
            },
            "test.nocodb_connection": {
                "summary": "🧪 STEP 1: Test NocoDB Connection",
                "description": "First, test your NocoDB connection and verify record structure",
                "value": "GET /test/nocodb?table_id=movxrk3kdcbkyo8&record_id=2"
            },
            "openai.generate_main": {
                "summary": "🚀 STEP 2: Generate Content (Main Test)",
                "description": "Generate AI content from your NocoDB record - Super simple, just provide record ID!",
                "value": "GET /openai.generate?id=2"
            },
            "openai.generate_custom_record": {
                "summary": "🎯 STEP 3: Test Any Record",
                "description": "Test with any record ID from your NocoDB table",
                "value": "GET /openai.generate?id=YOUR_RECORD_ID"
            },
            "test.nocodb_any_record": {
                "summary": "🔍 Debug: Check Any Record",
                "description": "Check the structure and data of any record in your table",
                "value": "GET /test/nocodb?table_id=movxrk3kdcbkyo8&record_id=YOUR_RECORD_ID"
            }
        }
    )
):
    try:
        obj_name, func_name = object_function.split(".", maxsplit=1)
    except ValueError:
        raise HTTPException(404, "Path must be object.function")

    try:
        service_mod = import_module(f"app.services.{obj_name}_service")
        service_cls = getattr(service_mod, obj_name.capitalize())
    except (ModuleNotFoundError, AttributeError):
        raise HTTPException(404, "Unknown object")

    # Convert Pydantic model to dict
    payload_dict = payload.model_dump() if hasattr(payload, 'model_dump') else {}

    service = service_cls(**payload_dict)
    try:
        func = getattr(service, func_name)
    except AttributeError:
        raise HTTPException(404, "Unknown function")

    result = await func(**payload_dict)

    # Handle different result types
    if result is None:
        return {"status": "success", "message": "Operation completed"}
    elif isinstance(result, dict):
        return result
    else:
        return {"status": "success", "result": result}


# Discovery endpoints for API documentation
@app.get("/routes", tags=["Discovery"], summary="List all registered FastAPI routes")
async def list_routes():
    """
    Returns all currently registered routes from FastAPI's perspective.
    Useful for debugging and seeing what FastAPI knows about.
    """
    return [
        {
            "path": route.path,
            "methods": list(route.methods) if hasattr(route, 'methods') else [],
            "name": route.name if hasattr(route, 'name') else None,
            "summary": getattr(route, 'summary', None)
        }
        for route in app.routes if isinstance(route, APIRoute)
    ]


@app.get("/available-endpoints", tags=["Discovery"], summary="List all available dynamic endpoints")
async def list_available_endpoints():
    """
    Returns all available service endpoints that can be called via the dynamic routing system.

    Format: `{service}.{method}`

    Each endpoint can be called as: `POST /{service}.{method}`
    """
    endpoints = []
    services_dir = "app/services"

    # Get all service files
    try:
        if os.path.exists(services_dir):
            service_files = [f for f in os.listdir(services_dir) if f.endswith('_service.py')]
        else:
            # Fallback: try to import known services
            service_files = ['autoit_service.py', 'facebook_service.py', 'line_service.py',
                           'minio_service.py', 'nocodb_service.py', 'openai_service.py']
    except:
        service_files = ['autoit_service.py', 'facebook_service.py', 'line_service.py',
                       'minio_service.py', 'nocodb_service.py', 'openai_service.py']

    for service_file in service_files:
        service_name = service_file.replace('_service.py', '')
        try:
            # Import the service module
            service_mod = import_module(f"app.services.{service_name}_service")
            service_cls = getattr(service_mod, service_name.capitalize())

            # Get all public methods (not starting with _)
            methods = [method for method in dir(service_cls)
                      if not method.startswith('_') and callable(getattr(service_cls, method))]

            # Add endpoints for this service
            for method in methods:
                endpoints.append(f"{service_name}.{method}")

        except (ImportError, AttributeError) as e:
            # Skip services that can't be imported
            continue

    return {
        "total_endpoints": len(endpoints),
        "endpoints": sorted(endpoints),
        "usage": "POST /{endpoint} with JSON payload"
    }


@app.get("/services", tags=["Discovery"], summary="Detailed information about all services")
async def list_services():
    """
    Returns detailed information about all available services including:
    - Service name and description
    - Available methods with their signatures
    - Example usage
    """
    services = {}
    service_descriptions = {
        "autoit": "AutoIt automation service for Windows GUI automation",
        "facebook": "Facebook API integration for posting and social media management",
        "line": "LINE messaging service for broadcasting messages",
        "minio": "MinIO object storage service for file uploads and management",
        "nocodb": "🖼️ NocoDB database service with AI Vision integration for automated content generation",
        "openai": "🖼️ OpenAI API integration with Vision (GPT-4V) capabilities for image analysis and AI-powered content generation"
    }

    services_dir = "app/services"

    # Get all service files
    try:
        if os.path.exists(services_dir):
            service_files = [f for f in os.listdir(services_dir) if f.endswith('_service.py')]
        else:
            service_files = ['autoit_service.py', 'facebook_service.py', 'line_service.py',
                           'minio_service.py', 'nocodb_service.py', 'openai_service.py']
    except:
        service_files = ['autoit_service.py', 'facebook_service.py', 'line_service.py',
                       'minio_service.py', 'nocodb_service.py', 'openai_service.py']

    for service_file in service_files:
        service_name = service_file.replace('_service.py', '')
        try:
            # Import the service module
            service_mod = import_module(f"app.services.{service_name}_service")
            service_cls = getattr(service_mod, service_name.capitalize())

            # Get all public methods
            methods = {}
            for method_name in dir(service_cls):
                if not method_name.startswith('_') and callable(getattr(service_cls, method_name)):
                    method = getattr(service_cls, method_name)

                    # Get method signature
                    try:
                        sig = inspect.signature(method)
                        params = []
                        for param_name, param in sig.parameters.items():
                            if param_name not in ['self', 'kwargs']:
                                param_info = {
                                    "name": param_name,
                                    "type": str(param.annotation) if param.annotation != param.empty else "Any",
                                    "default": str(param.default) if param.default != param.empty else None,
                                    "required": param.default == param.empty
                                }
                                params.append(param_info)

                        methods[method_name] = {
                            "parameters": params,
                            "endpoint": f"POST /{service_name}.{method_name}",
                            "description": method.__doc__.strip() if method.__doc__ else f"Execute {method_name} operation"
                        }
                    except Exception:
                        methods[method_name] = {
                            "parameters": [],
                            "endpoint": f"POST /{service_name}.{method_name}",
                            "description": f"Execute {method_name} operation"
                        }

            services[service_name] = {
                "description": service_descriptions.get(service_name, f"{service_name.capitalize()} service"),
                "class_name": service_cls.__name__,
                "methods": methods,
                "total_methods": len(methods)
            }

        except (ImportError, AttributeError) as e:
            services[service_name] = {
                "description": service_descriptions.get(service_name, f"{service_name.capitalize()} service"),
                "error": f"Could not load service: {str(e)}",
                "methods": {},
                "total_methods": 0
            }

    return {
        "total_services": len(services),
        "services": services,
        "authentication": "All endpoints require Bearer token authentication",
        "usage_examples": {
            "vision_integration": {
                "description": "🖼️ NEW: AI Vision Integration with NocoDB",
                "url": "GET /openai.generate?id={{row.id}}",
                "headers": {
                    "Authorization": "Bearer your-api-token"
                },
                "use_case": "Analyze images from NocoDB and generate content automatically"
            },
            "direct_vision": {
                "description": "🖼️ Direct Image Analysis",
                "url": "POST /openai.vision",
                "headers": {
                    "Authorization": "Bearer your-api-token",
                    "Content-Type": "application/json"
                },
                "body": {
                    "message": "Describe this image in detail",
                    "image_url": "https://example.com/image.jpg"
                }
            },
            "traditional_chat": {
                "description": "Traditional OpenAI Chat",
                "url": "POST /openai.request",
                "headers": {
                    "Authorization": "Bearer your-api-token",
                    "Content-Type": "application/json"
                },
                "body": {
                    "message": "Hello, world!",
                    "token": "your-openai-token"
                }
            }
        }
    }


@app.get("/", tags=["General"], summary="API Status and Quick Start Guide")
async def root():
    """
    Welcome endpoint with API status and quick start information.
    """
    return {
        "message": "Welcome to Common Class API",
        "status": "operational",
        "version": "1.0.0",
        "documentation": "/docs",
        "discovery_endpoints": {
            "/available-endpoints": "List all available service endpoints",
            "/services": "Detailed service information with method signatures",
            "/routes": "FastAPI registered routes"
        },
        "quick_start": {
            "1": "Get your API token from the .env file",
            "2": "Use Bearer token authentication in the Authorization header",
            "3": "Call any endpoint using POST /{service}.{method}",
            "4": "Check /available-endpoints for all available methods"
        },
        "featured_examples": {
            "vision_integration": {
                "description": "🖼️ NEW: AI Vision + NocoDB Integration",
                "url": "GET /openai.generate?id={{row.id}}",
                "headers": {
                    "Authorization": "Bearer super-secret-token"
                },
                "note": "Automatically analyze images and generate content!"
            },
            "connection_test": {
                "description": "🧪 STEP 1: Test NocoDB Connection",
                "url": "GET /test/nocodb?record_id=2",
                "headers": {
                    "Authorization": "Bearer super-secret-token"
                },
                "note": "First, verify your NocoDB connection and record structure",
                "config": {
                    "base_url": "http://www.amibigeye.com:8080",
                    "table_id": "movxrk3kdcbkyo8",
                    "record_id": "2"
                }
            },
            "content_generation": {
                "description": "🚀 STEP 2: Generate AI Content",
                "url": "GET /openai.generate?id=2",
                "headers": {
                    "Authorization": "Bearer super-secret-token"
                },
                "note": "Generate beautiful content from your NocoDB images using AI Vision",
                "config": {
                    "base_url": "http://www.amibigeye.com:8080",
                    "table_id": "movxrk3kdcbkyo8",
                    "record_id": "2",
                    "auto_fields": {
                        "image_field": "Image",
                        "content_field": "adjust_content",
                        "topic_field": "Topic"
                    }
                }
            },
            "direct_vision": {
                "description": "🖼️ Direct Image Analysis",
                "url": "POST /openai.vision",
                "headers": {
                    "Authorization": "Bearer super-secret-token",
                    "Content-Type": "application/json"
                },
                "body": {
                    "message": "What do you see in this image?",
                    "image_url": "https://picsum.photos/800/600"
                }
            },
            "traditional_chat": {
                "description": "Traditional OpenAI Chat",
                "url": "POST /openai.request",
                "headers": {
                    "Authorization": "Bearer super-secret-token",
                    "Content-Type": "application/json"
                },
                "body": {
                    "message": "Hello from Common Class API!"
                }
            }
        }
    }


@app.get("/health", tags=["General"], summary="Health check endpoint")
async def health_check():
    """
    Simple health check endpoint to verify the API is running.
    """
    return {
        "status": "healthy",
        "timestamp": "2024-01-01T00:00:00Z",
        "services_available": True
    }


@app.get("/line/send-to-all-friends/docs", tags=["LINE Documentation"], summary="Detailed documentation for send_to_all_friends method")
async def line_send_to_all_friends_docs():
    """
    Comprehensive documentation for the line.send_to_all_friends method including examples, parameters, and responses.
    """
    return {
        "method": "line.send_to_all_friends",
        "endpoint": "POST /line.send_to_all_friends",
        "description": "Send text, image, and/or video messages to all friends of a LINE token in one API call",
        "features": [
            "Send to all friends of the LINE token",
            "Support for text, image, and video content",
            "Automatic message object creation",
            "Comprehensive error handling",
            "Detailed response with sent content information"
        ],
        "parameters": {
            "token": {
                "type": "string",
                "required": True,
                "description": "LINE Channel Access Token"
            },
            "message": {
                "type": "string",
                "required": False,
                "description": "Text message to send (max 5000 characters)"
            },
            "img_url": {
                "type": "string",
                "required": False,
                "description": "Image URL (HTTPS, JPEG/PNG, max 10MB)"
            },
            "vd_url": {
                "type": "string",
                "required": False,
                "description": "Video URL (HTTPS, MP4, max 200MB)"
            }
        },
        "examples": {
            "text_only": {
                "token": "your_line_token",
                "message": "Hello everyone! 🎉"
            },
            "text_with_image": {
                "token": "your_line_token",
                "message": "Check out this image!",
                "img_url": "https://picsum.photos/800/600"
            },
            "all_content_types": {
                "token": "your_line_token",
                "message": "Complete package!",
                "img_url": "https://picsum.photos/800/600",
                "vd_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
            }
        },
        "response_examples": {
            "success": {
                "status": "success",
                "message": "Successfully sent text, image, video to all friends",
                "content_types": ["text", "image", "video"],
                "text_message": "Complete package!",
                "image_url": "https://picsum.photos/800/600",
                "video_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
                "method": "send_to_all_friends",
                "message_count": 3
            },
            "error_no_content": {
                "status": "error",
                "error": "At least one of message, img_url, or vd_url must be provided"
            }
        },
        "line_api_limits": {
            "max_messages_per_broadcast": 5,
            "text_max_characters": 5000,
            "image_max_size": "10MB",
            "video_max_size": "200MB",
            "supported_image_formats": ["JPEG", "PNG"],
            "supported_video_formats": ["MP4"],
            "url_requirements": "HTTPS only"
        },
        "testing": {
            "test_endpoint": "POST /line.test_send_to_all_friends",
            "description": "Use this endpoint to test without making real LINE API calls",
            "test_examples": {
                "all_content_types": {
                    "token": "test_token_123",
                    "message": "This is a test message",
                    "img_url": "https://example.com/test-image.jpg",
                    "vd_url": "https://example.com/test-video.mp4"
                },
                "text_only": {
                    "token": "test_token_123",
                    "message": "Just testing text message functionality"
                },
                "image_only": {
                    "token": "test_token_123",
                    "img_url": "https://picsum.photos/800/600"
                },
                "video_only": {
                    "token": "test_token_123",
                    "vd_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
                },
                "error_case": {
                    "token": "test_token_123"
                }
            }
        }
    }


@app.get("/line/test-send-to-all-friends/params", tags=["LINE Documentation"], summary="Test parameters for send_to_all_friends method")
async def line_test_send_to_all_friends_params():
    """
    Detailed test parameters and examples for the line.test_send_to_all_friends method.
    This endpoint shows all possible parameter combinations for testing.
    """
    return {
        "method": "line.test_send_to_all_friends",
        "endpoint": "POST /line.test_send_to_all_friends",
        "description": "Test the send_to_all_friends method without making actual LINE API calls",
        "note": "This is a safe testing method that simulates the real functionality without calling LINE API",
        "parameters": {
            "token": {
                "type": "string",
                "required": False,
                "description": "Test token (can be any string)",
                "default": "test_token",
                "example": "test_token_123"
            },
            "message": {
                "type": "string",
                "required": False,
                "description": "Test text message",
                "example": "This is a test message"
            },
            "img_url": {
                "type": "string",
                "required": False,
                "description": "Test image URL",
                "example": "https://picsum.photos/800/600"
            },
            "vd_url": {
                "type": "string",
                "required": False,
                "description": "Test video URL",
                "example": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
            }
        },
        "test_scenarios": {
            "1_all_content_types": {
                "description": "Test with all content types",
                "payload": {
                    "token": "test_token_123",
                    "message": "Testing all content types together",
                    "img_url": "https://picsum.photos/800/600",
                    "vd_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
                },
                "expected_response": {
                    "status": "success",
                    "content_types": ["text", "image", "video"],
                    "message_count": 3,
                    "test_mode": True
                }
            },
            "2_text_only": {
                "description": "Test with text message only",
                "payload": {
                    "token": "test_token_123",
                    "message": "Just testing text message"
                },
                "expected_response": {
                    "status": "success",
                    "content_types": ["text"],
                    "message_count": 1,
                    "test_mode": True
                }
            },
            "3_image_only": {
                "description": "Test with image only",
                "payload": {
                    "token": "test_token_123",
                    "img_url": "https://picsum.photos/800/600"
                },
                "expected_response": {
                    "status": "success",
                    "content_types": ["image"],
                    "message_count": 1,
                    "test_mode": True
                }
            },
            "4_video_only": {
                "description": "Test with video only",
                "payload": {
                    "token": "test_token_123",
                    "vd_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
                },
                "expected_response": {
                    "status": "success",
                    "content_types": ["video"],
                    "message_count": 1,
                    "test_mode": True
                }
            },
            "5_error_case": {
                "description": "Test error case - no content provided",
                "payload": {
                    "token": "test_token_123"
                },
                "expected_response": {
                    "status": "error",
                    "error": "At least one of message, img_url, or vd_url must be provided"
                }
            }
        },
        "how_to_test": {
            "step_1": "Go to Swagger UI at /docs",
            "step_2": "Find the POST /{object_function} endpoint",
            "step_3": "In the object_function field, enter: line.test_send_to_all_friends",
            "step_4": "Choose one of the test scenarios from above",
            "step_5": "Copy the payload into the request body",
            "step_6": "Click 'Try it out' to test"
        }
    }
