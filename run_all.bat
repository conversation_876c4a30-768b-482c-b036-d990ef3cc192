@echo off
REM Common Class API - Run All Script
REM This script sets up and runs the FastAPI application

echo 🚀 Common Class API - Setup and Run
echo.

REM Check if virtual environment exists
if not exist ".venv" (
    echo 🔧 Creating virtual environment...
    python -m venv .venv
    if errorlevel 1 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM Activate virtual environment and install dependencies
echo 📦 Installing dependencies...
call .venv\Scripts\activate.bat
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

REM Create .env file if it doesn't exist
if not exist ".env" (
    echo ⚙️  Creating environment configuration...
    copy .env.example .env
)

REM Start the application
echo.
echo 🚀 Starting FastAPI application...
echo 📖 API Documentation will auto-open at: http://127.0.0.1:8000/docs
echo 🛑 Press Ctrl+C to stop the server
echo.

REM Start the server in background and wait for it to be ready
start /B uvicorn app.main:app --reload --host 127.0.0.1 --port 8000

REM Wait for server to start and then open browser
echo ⏳ Waiting for server to start...
timeout /t 3 /nobreak >nul
echo 🌐 Opening Swagger documentation...
start http://127.0.0.1:8000/docs

REM Keep the window open and show server status
echo.
echo ✅ Server is running and Swagger documentation opened!
echo 📖 Swagger UI: http://127.0.0.1:8000/docs
echo 🔗 API Endpoints: http://127.0.0.1:8000/available-endpoints
echo 🛑 Press Ctrl+C to stop the server
echo.

REM Wait for user to stop the server
pause
