# Common Class API Makefile

.PHONY: help install setup run dev clean test all

# Default target
all: setup run

help:
	@echo "Available targets:"
	@echo "  all      - Setup and run the application (default)"
	@echo "  setup    - Create virtual environment and install dependencies"
	@echo "  install  - Install dependencies only"
	@echo "  run      - Run the FastAPI application"
	@echo "  dev      - Run in development mode with auto-reload"
	@echo "  clean    - Remove virtual environment and cache files"
	@echo "  test     - Run tests (when available)"

setup: .venv .env
	@echo "✅ Setup complete!"

.venv:
	@echo "🔧 Creating virtual environment..."
	python -m venv .venv
	@echo "📦 Installing dependencies..."
	.venv/Scripts/activate && pip install -r requirements.txt

.env:
	@echo "⚙️  Creating environment configuration..."
	cp .env.example .env

install:
	@echo "📦 Installing dependencies..."
	.venv/Scripts/activate && pip install -r requirements.txt

run: setup
	@echo "🚀 Starting FastAPI application..."
	@echo "📖 Swagger documentation will auto-open at: http://127.0.0.1:8000/docs"
	@(sleep 3 && start http://127.0.0.1:8000/docs) &
	.venv/Scripts/activate && uvicorn app.main:app --host 127.0.0.1 --port 8000

dev: setup
	@echo "🧪 Starting FastAPI application in development mode..."
	@echo "📖 Swagger documentation will auto-open at: http://127.0.0.1:8000/docs"
	@(sleep 3 && start http://127.0.0.1:8000/docs) &
	.venv/Scripts/activate && uvicorn app.main:app --reload --host 127.0.0.1 --port 8000

test:
	@echo "🧪 Running tests..."
	.venv/Scripts/activate && python -m pytest tests/ -v

clean:
	@echo "🧹 Cleaning up..."
	rm -rf .venv
	rm -rf __pycache__
	rm -rf app/__pycache__
	rm -rf app/core/__pycache__
	rm -rf app/services/__pycache__
	find . -name "*.pyc" -delete
	find . -name "*.pyo" -delete
