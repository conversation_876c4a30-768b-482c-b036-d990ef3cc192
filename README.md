# Common Class API

A plug‑and‑play FastAPI microservice exposing reusable helper classes through dynamic routes of the form `/object.function`.
Designed to sit behind Cloudflare Tunnel at `https://common_class_api.ultraagent.app`.

## Quick start (dev)

### 🚀 Auto-open Swagger (Recommended)
```bash
# Enhanced startup with auto-opening Swagger documentation
python start_with_swagger.py
```

### 🔧 Platform-specific startup scripts
```bash
# Windows (Batch)
run_all.bat

# Windows (PowerShell)
.\run_all.ps1

# Cross-platform (Make)
make dev

# Simple Python runner
python run.py
```

### 📖 Manual setup
```bash
python -m venv .venv && source .venv/bin/activate
pip install -r requirements.txt
cp .env.example .env
uvicorn app.main:app --reload --port 8000
# Then manually open: http://127.0.0.1:8000/docs
```

## Docker

```bash
docker compose up --build -d
```

## API Discovery

Since this API uses dynamic routing, the Swagger UI (`/docs`) only shows the generic dynamic endpoint. Use these discovery endpoints to explore available services:

### Discovery Endpoints

- **`GET /`** - API status and quick start guide
- **`GET /available-endpoints`** - List all available service endpoints
- **`GET /services`** - Detailed service information with method signatures
- **`GET /routes`** - FastAPI registered routes (for debugging)
- **`GET /health`** - Health check endpoint

### Available Services

- **`autoit`** - AutoIt automation service for Windows GUI automation
- **`facebook`** - Facebook API integration for posting and social media management
- **`line`** - **Complete LINE messaging service** with 25+ methods for broadcasting, push messages, rich media, user management, and webhooks
- **`minio`** - MinIO object storage service for file uploads and management
- **`nocodb`** - NocoDB database service for database operations
- **`openai`** - OpenAI API integration for AI-powered requests

### Usage Example

```bash
# Get all available endpoints
curl http://127.0.0.1:8000/available-endpoints

# Call a service method
curl -X POST "http://127.0.0.1:8000/openai.request" \
  -H "Authorization: Bearer super-secret-token" \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello, world!"}'
```

### Authentication

All dynamic endpoints require Bearer token authentication. Set your `API_TOKEN` in the `.env` file.

## LINE Service - Complete Implementation

The LINE messaging service is fully implemented with comprehensive functionality:

### 🚀 **Core Features**
- **Broadcasting** to all friends (`line.broadcast`)
- **Push messaging** to specific users (`line.push`)
- **Reply messaging** with reply tokens (`line.reply`)
- **Multicast** to multiple users (`line.multicast`)

### 📱 **Rich Message Types**
- Text messages with emojis
- Image messages (JPEG/PNG, up to 10MB)
- Video messages (MP4, up to 200MB)
- Audio messages (M4A, up to 200MB)
- Location messages with coordinates
- Sticker messages from LINE packages

### 👥 **User Management**
- Get user profiles (`line.get_profile`)
- Group member management
- Room member management
- Leave groups/rooms

### 🔧 **Utilities**
- Bot information (`line.get_bot_info`)
- Quota monitoring (`line.get_quota`, `line.get_quota_consumption`)
- Webhook signature validation
- Batch operations

### 📊 **Current Status**
✅ **25+ methods implemented**
✅ **Production ready** with error handling
✅ **Real LINE API integration** (tested with live token)
✅ **Comprehensive documentation** and examples

### 🧪 **Testing**
```bash
# Test the LINE service
python test_line_service.py

# Run usage examples
python line_examples.py
```

### 📖 **Documentation**
See `LINE_SERVICE_DOCUMENTATION.md` for complete API reference and examples.
